import Ticket from "./../models/ticketModel.js";
import * as factory from "./handlerFactory.js";
import {addConversation} from "../utils/ticketUtils.js";

// Create a new ticket
export const createTicket = async (req, res, next) => {
  try {
    const ticketData = req.body;
    if (req.user && req.user._id) {
      ticketData.user = req.user._id;
    }
    const ticket = await Ticket.create(ticketData);
    res.status(201).json({ status: 'success', data: ticket });
  } catch (err) {
    next(err);
  }
};

// Get all tickets (admin only or ticket owner)
export const getAllTickets = async (req, res, next) => {
  try {
    let filter = {};
    if (!req.user || req.user.role !== 'admin') {
      filter.user = req.user._id;
    }
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;
    const total = await Ticket.countDocuments(filter);
    const tickets = await Ticket.find(filter)
      .populate('user', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    const pages = Math.ceil(total / limit);
    res.status(200).json({
      status: 'success',
      results: tickets.length,
      pagination: {
        currentPage: page,
        pages,
        total,
        hasNextPage: page < pages,
        hasPrevPage: page > 1
      },
      data: tickets,
    });
  } catch (err) {
    next(err);
  }
};

// Get a ticket by ID
export const getTicketById = factory.getOne(Ticket);

// Update a ticket's status
export const updateTicketStatus = factory.updateOne(Ticket);

// Delete a ticket
export const deleteTicket = factory.deleteOne(Ticket);
